"use client";
import { useState, useCallback, useMemo } from "react";
import React from "react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { NavigationMenuLink } from "@/components/ui/navigation-menu";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import { ArrowUpRight } from "lucide-react";

export interface NavigationItem {
  title: string;
  href?: string;
  description?: string;
  icon?: React.ReactNode;
  items?: NavigationItem[];
  isMegaMenu?: boolean;
}

interface DynamicMegaMenuProps {
  services: NavigationItem[];
}

// Separate component for main service item
const MainServiceItem = React.memo(
  ({
    service,
    isHovered,
    onMouseEnter,
    onMouseLeave,
    translations,
  }: {
    service: NavigationItem;
    isHovered: boolean;
    onMouseEnter: () => void;
    onMouseLeave: () => void;
    translations: {
      t: (key: string) => string;
      tService: (key: string) => string;
    };
  }) => {
    const { t, tService } = translations;

    return (
      <div
        className={cn(
          "group block select-none rounded-lg p-3 leading-none outline-none transition-all duration-200 cursor-default"
        )}
        style={{
          backgroundColor: isHovered ? "#5840BA1A" : undefined,
          color: isHovered ? "#5840BA" : "#636363",
        }}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
      >
        <div className="flex items-center gap-3">
          {service.icon && React.isValidElement(service.icon) && (
            <div
              className={cn(
                "inline-flex items-center [&_svg]:transition-colors",
                isHovered
                  ? "[&_svg]:!fill-[#5840BA] [&_svg]:!stroke-[#5840BA]"
                  : "[&_svg]:!fill-[#636363] [&_svg]:!stroke-[#636363]"
              )}
            >
              {service.icon}
            </div>
          )}
          <div className="flex-1">
            <div className="text-base font-semibold leading-tight">
              {t(service.title) || tService(service.title) || service.title}
            </div>
          </div>
        </div>
      </div>
    );
  }
);

MainServiceItem.displayName = "MainServiceItem";

// Separate component for sub-service item
const SubServiceItem = React.memo(
  ({
    subService,
    isHovered,
    onMouseEnter,
    onMouseLeave,
    translations,
  }: {
    subService: NavigationItem;
    isHovered: boolean;
    onMouseEnter: () => void;
    onMouseLeave: () => void;
    translations: { tService: (key: string) => string };
  }) => {
    const { tService } = translations;

    return (
      <NavigationMenuLink asChild>
        <Link
          href={subService.href || "#"}
          className="block select-none py-3 px-0 leading-none no-underline outline-none transition-all duration-200 hover:bg-white focus:bg-white"
          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
        >
          <div className="flex items-start gap-3">
            {subService.icon && React.isValidElement(subService.icon) && (
              <div
                className={cn(
                  "inline-flex items-center [&_svg]:transition-colors [&_svg]:duration-200 flex-shrink-0 mt-0.5",
                  isHovered
                    ? "[&_svg]:!fill-[#5840BA] [&_svg]:!stroke-[#5840BA] [&_svg]:!color-[#5840BA] [&_svg_path]:!fill-[#5840BA] [&_svg_path]:!stroke-[#5840BA] [&_svg_circle]:!fill-[#5840BA] [&_svg_circle]:!stroke-[#5840BA]"
                    : "[&_svg]:!fill-[#636363] [&_svg]:!stroke-[#636363] [&_svg]:!color-[#636363] [&_svg_path]:!fill-[#636363] [&_svg_path]:!stroke-[#636363] [&_svg_circle]:!fill-[#636363] [&_svg_circle]:!stroke-[#636363]"
                )}
                style={{
                  color: isHovered ? "#5840BA" : "#636363",
                }}
              >
                {subService.icon}
              </div>
            )}
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <div
                  className={cn(
                    "leading-tight transition-all duration-200 font-semibold"
                  )}
                  style={{
                    fontFamily: "IBM Plex Sans Arabic",
                    fontSize: "16px",
                    lineHeight: "20.8px",
                    letterSpacing: "0%",
                    color: isHovered ? "#5840BA" : "#636363",
                  }}
                >
                  {tService(subService.title) || subService.title}
                </div>
                {isHovered && (
                  <ArrowUpRight
                    className="w-4 h-4 flex-shrink-0 transition-opacity duration-200"
                    style={{ color: "#5840BA" }}
                  />
                )}
              </div>
              {subService.description && (
                <p
                  className="leading-snug mt-1"
                  style={{
                    fontFamily: "IBM Plex Sans Arabic",
                    fontSize: "12px",
                    lineHeight: "15.6px",
                    letterSpacing: "0%",
                    fontWeight: 400,
                    color: "#636363",
                  }}
                >
                  {tService(subService.description) || subService.description}
                </p>
              )}
            </div>
          </div>
        </Link>
      </NavigationMenuLink>
    );
  }
);

SubServiceItem.displayName = "SubServiceItem";

// Main component
export function DynamicMegaMenu({ services }: DynamicMegaMenuProps) {
  const t = useTranslations("MegaMenu");
  const tService = useTranslations("ServiceItems");

  // Set first service as default hovered to prevent glitch
  const [hoveredService, setHoveredService] = useState<string | null>(
    services.length > 0 ? services[0].title : null
  );
  const [hoveredSubService, setHoveredSubService] = useState<string | null>(
    null
  );

  // Memoize the current hovered service to avoid recalculation
  const currentHoveredService = useMemo(
    () => services.find((service) => service.title === hoveredService),
    [services, hoveredService]
  );

  // Optimized hover handlers
  const handleServiceHover = useCallback((serviceTitle: string) => {
    setHoveredService(serviceTitle);
    setHoveredSubService(null);
  }, []);

  const handleServiceLeave = useCallback(
    (hasSubItems: boolean) => {
      if (!hasSubItems) {
        setHoveredService(services.length > 0 ? services[0].title : null);
      }
    },
    [services]
  );

  const handleSubServiceHover = useCallback((subServiceTitle: string) => {
    setHoveredSubService(subServiceTitle);
  }, []);

  const handleSubServiceLeave = useCallback(() => {
    setHoveredSubService(null);
  }, []);

  const handleSubServicesContainerLeave = useCallback(() => {
    setHoveredService(services.length > 0 ? services[0].title : null);
    setHoveredSubService(null);
  }, [services]);

  // Memoize translations
  const translations = useMemo(() => ({ t, tService }), [t, tService]);

  // Check if we have sub-services to show
  const hasSubServices =
    currentHoveredService?.items && currentHoveredService.items.length > 0;

  return (
    <div
      className={cn(
        "p-4 flex flex-col",
        hasSubServices ? "w-[800px]" : "w-[400px]"
      )}
    >
      {/* Main Content */}
      <div className="flex gap-4">
        {/* Main Services Section */}
        <div
          className={cn(
            "flex flex-col",
            hasSubServices ? "w-[380px]" : "flex-1"
          )}
        >
          {/* Main Services Title */}
          <div className="mb-3 px-3">
            <h2 className="text-sm font-medium text-gray-500 text-right">
              {t("services")}
            </h2>
          </div>

          {/* Main Services List */}
          <div className="flex flex-col gap-2">
            {services.map((service) => {
              const hasSubItems = Boolean(service.items?.length);
              return (
                <MainServiceItem
                  key={service.title}
                  service={service}
                  isHovered={hoveredService === service.title}
                  onMouseEnter={() => handleServiceHover(service.title)}
                  onMouseLeave={() => handleServiceLeave(hasSubItems)}
                  translations={translations}
                />
              );
            })}

            {/* All Services Button */}
            <div className="flex justify-end mt-2">
              <NavigationMenuLink asChild>
                <Link
                  href="/services"
                  className="flex flex-row font-bold transition-all group/link text-sm hover:bg-[#5840BA1A] rounded-lg p-2 items-center"
                  style={{ color: "#5840BA" }}
                >
                  <span className="ml-1">{t("allServices")}</span>
                  <ArrowUpRight style={{ color: "#5840BA" }} />
                </Link>
              </NavigationMenuLink>
            </div>
          </div>
        </div>

        {/* Vertical Separator */}
        <div className="h-auto flex flex-col">
          <Separator
            orientation="vertical"
            className="h-full"
            style={{ borderColor: "#E5E7EB", borderWidth: "1px" }}
          />
        </div>

        {/* Sub-services Section */}
        {hasSubServices && (
          <div className="flex-1 flex flex-col">
            {/* Sub-services Title */}
            <div className="mb-3 px-3">
              <h3
                className="text-right uppercase"
                style={{
                  fontFamily: "IBM Plex Sans Arabic",
                  fontWeight: 500,
                  fontSize: "12px",
                  lineHeight: "16px",
                  letterSpacing: "0.2px",
                  color: "#6B7280",
                }}
              >
                {t("subServices")}
              </h3>
            </div>

            {/* Sub-services Panel */}
            <div
              className="flex-1 rounded-lg px-3 animate-in fade-in duration-200"
              onMouseEnter={() =>
                setHoveredService(currentHoveredService.title)
              }
              onMouseLeave={handleSubServicesContainerLeave}
            >
              <div className="h-full flex flex-col">
                {currentHoveredService.items?.map((subService, index) => (
                  <div key={subService.title}>
                    <SubServiceItem
                      subService={subService}
                      isHovered={hoveredSubService === subService.title}
                      onMouseEnter={() =>
                        handleSubServiceHover(subService.title)
                      }
                      onMouseLeave={handleSubServiceLeave}
                      translations={translations}
                    />
                    {index < (currentHoveredService.items?.length || 0) - 1 && (
                      <Separator
                        className="my-1"
                        style={{ borderColor: "#F3F4F6" }}
                      />
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
