"use client";
import CEOSection from "@/app/components/CEOSection/CeoSection";
import ContactSection from "@/app/components/ContactSection /ContactSection";
import HeroSection from "@/app/components/HeroSection/HeroSection";
import NumbersSection from "@/app/components/NumbersSection/NumbersSection";
import PartnersSection from "@/app/components/PartenersSection/PartenersSection";
import ServicesSection from "@/app/components/ServicesSection /ServicesSection";
import TeamSection from "@/app/components/TeamSection /TeamSecton";
import VisionSection from "@/app/components/VisionSection/VisionSection";
import Image from "next/image";

export default function Home() {
  return (
    <div className=" ">
      <HeroSection />
      <Image
        src="/SectionBreak.png"
        alt="Description of image"
        sizes="100vw"
        style={{ width: "100%", height: "24px", opacity: "80%" }}
        width={0}
        height={0}
      />
      <CEOSection />
      <Image
        src="/SectionBreak.png"
        alt="Description of image"
        sizes="100vw"
        style={{ width: "100%", height: "24px", opacity: "80%" }}
        width={0}
        height={0}
      />
      <TeamSection />
      <Image
        src="/SectionBreak.png"
        alt="Description of image"
        sizes="100vw"
        style={{ width: "100%", height: "24px", opacity: "80%" }}
        width={0}
        height={0}
      />
      <ServicesSection />
      <Image
        src="/SectionBreak.png"
        alt="Description of image"
        sizes="100vw"
        style={{ width: "100%", height: "24px", opacity: "80%" }}
        width={0}
        height={0}
      />
      <VisionSection />
      <Image
        src="/SectionBreak.png"
        alt="Description of image"
        sizes="100vw"
        style={{ width: "100%", height: "24px", opacity: "80%" }}
        width={0}
        height={0}
      />
      <NumbersSection />
      <Image
        src="/SectionBreak.png"
        alt="Description of image"
        sizes="100vw"
        style={{ width: "100%", height: "24px", opacity: "80%" }}
        width={0}
        height={0}
      />
      <PartnersSection />

    </div>
  );
}
