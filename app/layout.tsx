
import type { Metada<PERSON> } from "next";
import "./globals.css";
import { IBM_Plex_Sans_Arabic } from "next/font/google";
import { NextIntlClientProvider } from "next-intl";
import { getLocale } from "next-intl/server";
import DirectionWrapper from "./DirectionWrapper";
import Header from "@/components/Header/Header";
import Footer from "@/components/Footer/Footer";

const ibmPlexSansArabic = IBM_Plex_Sans_Arabic({
  subsets: ["arabic"],
  weight: ["100", "200", "300", "400", "500", "600", "700"],
  variable: "--font-ibm-plex-arabic",
  display: "swap",
});

export const metadata: Metadata = {
  title: "GeoTech",
  description: "جيوتك حلول رقمية متكاملة تقود تحول الأعمال في العصر الذكي",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const locale = await getLocale();

  return (
    <html lang={locale} dir={locale === "ar" ? "rtl" : "ltr"}>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link
          rel="apple-touch-icon"
          sizes="180x180"
          href="/apple-touch-icon.png"
        />
        <link
          rel="icon"
          type="image/png"
          sizes="32x32"
          href="/favicon-32x32.png"
        />
        <link
          rel="icon"
          type="image/png"
          sizes="16x16"
          href="/favicon-16x16.png"
        />
        <link rel="manifest" href="/site.webmanifest" />
        <meta property="og:title" content="GeoTech" />
        <meta
          property="og:description"
          content="جيوتك حلول رقمية متكاملة تقود تحول الأعمال في العصر الذكي"
        />
        <meta property="og:image" content="/FooterLogo.svg" />
        <meta property="og:url" content="https://gt.com.sa" />
        <meta property="og:type" content="website" />
      </head>
      <body className={`${ibmPlexSansArabic.variable} antialiased`}>
        <NextIntlClientProvider>
          <DirectionWrapper dir={locale === "ar" ? "rtl" : "ltr"}>
            <Header />
            {children}
            <Footer />
          </DirectionWrapper>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
