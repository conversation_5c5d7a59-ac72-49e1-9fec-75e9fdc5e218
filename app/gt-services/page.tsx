"use client";

import React, { useState } from "react";
import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowUpRight } from "lucide-react";
import { services } from "@/app/data/navItems";
import { useTranslations } from "next-intl";
import { SectionHeader } from "@/components/SectionHeader/SectionHeader";
import { Section } from "@/components/Section/Section";

const Page = () => {
  const [hoveredService, setHoveredService] = useState<string | null>(null);
  const t = useTranslations("ServicesSection");

  return (
    <Section variant="white" padding="default" relative>
      <div className="container mx-auto px-4 relative mt-30 z-10">
    
        {/* Services Sections */}
        <div className="space-y-16">
          {services.items.map((service, serviceIndex) => (
            <div key={serviceIndex} className="space-y-8">
              {/* Service Title and Description */}
              <div className="text-center space-y-4">
                <div className="flex justify-center items-center gap-4">
                  <div className="text-[#5840BA] w-[48px] h-[48px]">
                    {service.icon}
                  </div>
                </div>
                <h2 className="text-3xl font-bold text-gray-900">
                  {t(service.title)}
                </h2>
                <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                  {t(service.description)}
                </p>
              </div>

              {/* Sub-services Cards */}
              {service.items && service.items.length > 0 && (
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 justify-center">
                  {service.items.map((subService, subIndex) => (
                    <Card
                      key={subIndex}
                      className={`bg-[url(/ServicesMask.svg)] md:bg-[url(/ServicesMaskBW.svg)] md:hover:bg-[url(/ServicesMask.svg)] bg-no-repeat top-0 left-0 group relative w-full max-w-[300px] mx-auto h-[250px] transition-all duration-300 cursor-pointer shadow-none border-2 ${
                        hoveredService === subService.title
                          ? "border-[#5840BA] shadow-[0px_0px_9px_0px_#5840BABF] bg-white"
                          : "border-[#ECECEC] hover:border-[#5840BA] bg-white"
                      }`}
                      onMouseEnter={() => setHoveredService(subService.title)}
                      onMouseLeave={() => setHoveredService(null)}
                    >
                      <CardContent
                        className={`h-full flex flex-col transition-all duration-300 ${
                          hoveredService === subService.title
                            ? "pt-7"
                            : "pt-7 sm:pt-10 lg:pt-18"
                        }`}
                      >
                        {/* Icon */}
                        <div className="mb-[8px]">
                          <div
                            className={`transition-colors w-[36px] h-[36px] duration-300 ${
                              hoveredService === subService.title
                                ? "text-[#5840BA]"
                                : "text-[#5840BA] md:text-[#6D6D6D]"
                            }`}
                          >
                            {subService.icon}
                          </div>
                        </div>

                        {/* Content */}
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-[#424242] mb-[12px] leading-tight hover:text-[#000]">
                            {t(`ServiceItems.${subService.title}`)}
                          </h3>
                          <p className="text-gray-600 text-sm leading-relaxed">
                            {t(`ServiceItems.${subService.description}`)}
                          </p>
                        </div>

                        <div
                          className={`transition-opacity duration-300 ${
                            hoveredService === subService.title
                              ? "opacity-100 inline-flex items-center text-[#5840BA] hover:text-[#5840BA] font-medium group/link text-sm"
                              : "lg:opacity-0 lg:invisible opacity-100 text-[#5840BA] text-sm flex mt-2"
                          }`}
                        >
                          <Link
                            href={subService.href}
                            className="flex underline items-center"
                          >
                            <span>
                              {t("moreAbout")}{" "}
                              {t(`ServiceItems.${subService.title}`).split(" ").slice(0, 2).join(" ")}
                            </span>
                            <ArrowUpRight className="w-4 h-4 mr-2" />
                          </Link>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </Section>
  );
};

export default Page;
