"use client";

import React, { useState } from "react";
import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { services } from "@/app/data/navItems";
import { Section } from "@/components/Section/Section";
import { ArrowUpRight } from "lucide-react";
import { useTranslations, useLocale } from "next-intl";

const Page = () => {
  const [hoveredService, setHoveredService] = useState<string | null>(null);
  const t = useTranslations("ServicesSection");
  const tItems = useTranslations("ServiceItems");

  const locale = useLocale();
  return (
    <div>
      <div className="py-4 mt-30 md:py-5 relative overflow-hidden">
        <div className="absolute inset-0 bg-[#4730A4]" />
        <div
          className={`bg-[url(/contactFlowMobile.svg)] md:bg-[url(/contactFlow.svg)] w-full absolute inset-0 left-0 bg-no-repeat bg-center bg-cover mix-blend-luminosity ${
            locale === "en" ? "rotate-0" : "rotate-180"
          }`}
        />
        <div className="relative z-10 container mx-auto">
          <div className="w-full px-4  text-start text-white mx-auto ">
            <h2 className="text-2xl sm:text-xl md:text-[28px] font-bold mb-4 md:mb-6">
              {t("heading")}
            </h2>
            <p className="text-[12px] md:text-sm mb-6 md:mb-8 text-white/90 leading-relaxed">
              {t("description")}
            </p>
          </div>
        </div>
      </div>
      <Section variant="white" padding="default" relative>
        <div className="container mx-auto px-4 relative mt-30 z-10">
          {/* Services Sections */}
          <div className="space-y-16">
            {services.items.map((service, serviceIndex) => (
              <div key={serviceIndex} className="space-y-8">
                {/* Service Title and Description */}
                <div className="text-start space-y-4">
                  <h2 className="text-3xl font-bold text-gray-900">
                    {t(service.title)}
                  </h2>
                  <p className="text-base text-[#525252] ">
                    {t(service.description)}
                  </p>
                </div>

                {/* Sub-services Cards */}
                {service.items && service.items.length > 0 && (
                  <div className="grid md:grid-cols-2 lg:grid-cols-3  justify-center">
                    {service.items.map((subService, subIndex) => (
                      <Card
                        key={subIndex}
                        className={`bg-[url(/ServicesMask.svg)] md:bg-[url(/ServicesMaskBW.svg)] md:hover:bg-[url(/ServicesMask.svg)] bg-no-repeat top-0 left-0 group relative w-full max-w-[322px] mx-auto h-[192px] transition-all duration-300 cursor-pointer shadow-none border-2 ${
                          hoveredService === subService.title
                            ? "border-[#5840BA] shadow-[0px_0px_9px_0px_#5840BABF] bg-white"
                            : "border-[#ECECEC] hover:border-[#5840BA] bg-white"
                        }`}
                        onMouseEnter={() => setHoveredService(subService.title)}
                        onMouseLeave={() => setHoveredService(null)}
                      >
                        <CardContent
                          className={`h-full flex flex-col transition-all duration-300 ${
                            hoveredService === subService.title
                              ? "pt-0"
                              : "pt-0"
                          }`}
                        >
                          {/* Icon */}
                          <div className="mb-[8px]">
                            <div
                              className={`transition-colors w-[36px] h-[36px] duration-300 ${
                                hoveredService === subService.title
                                  ? "text-[#5840BA] [&_svg]:!fill-[#5840BA] [&_svg]:!stroke-[#5840BA] [&_svg]:!color-[#5840BA] [&_svg_path]:!fill-[#5840BA] [&_svg_path]:!stroke-[#5840BA] [&_svg_circle]:!fill-[#5840BA] [&_svg_circle]:!stroke-[#5840BA]"
                                  : "text-[#5840BA] md:text-[#6D6D6D] [&_svg]:!fill-[#636363] [&_svg]:!stroke-[#636363] [&_svg]:!color-[#636363] [&_svg_path]:!fill-[#636363] [&_svg_path]:!stroke-[#636363] [&_svg_circle]:!fill-[#636363] [&_svg_circle]:!stroke-[#636363]"
                              }`}
                            >
                              {subService.icon}
                            </div>
                          </div>

                          {/* Content */}
                          <div className="flex-1">
                            <h3 className="text-lg font-semibold text-[#424242] mb-[12px] leading-tight hover:text-[#000]">
                              {tItems(subService.title)}
                            </h3>
                            <p className="text-gray-600 text-sm leading-relaxed">
                              {tItems(subService.description)}
                            </p>
                          </div>

                          <div
                            className={`transition-opacity duration-300 ${
                              hoveredService === subService.title
                                ? "opacity-100 inline-flex items-center text-[#5840BA] hover:text-[#5840BA] font-medium group/link text-sm"
                                : "lg:opacity-0 lg:invisible opacity-100 text-[#5840BA] text-sm flex mt-2"
                            }`}
                          >
                            <Link
                              href={subService.href}
                              className="flex underline items-center"
                            >
                              <span>
                                {t("moreAbout")}{" "}
                                {tItems(subService.title)
                                  .split(" ")
                                  .slice(0, 2)
                                  .join(" ")}
                              </span>
                              <ArrowUpRight className="w-4 h-4 mr-2" />
                            </Link>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </Section>
    </div>
  );
};

export default Page;
